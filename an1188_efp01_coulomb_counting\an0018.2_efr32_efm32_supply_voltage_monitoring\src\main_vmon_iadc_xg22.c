/**************************************************************************//**
 * @file
 * @brief Uses the IADC as a window comparator on a single pin. Input is taken
 * on PC04 (P25 on BRD4001 J102) and PB01 (WSTK LED0) toggles on each
 * comparator trigger. The most recent sample within the window comparison is
 * also stored globally.
 * @version  1.10

 ******************************************************************************
 * @section License
 * <b>(C) Copyright 2020 Silicon Labs, http://www.silabs.com</b>
 ******************************************************************************
 *
 * This file is licensed under the Silicon Labs Software License Agreement. See
 * "http://developer.silabs.com/legal/version/v11/
 * Silicon_Labs_Software_License_Agreement.txt"
 * for details. Before using this software for any purpose, you must agree to
 * the terms of that agreement.
 *
 *****************************************************************************/

/**************************************************************************//**
 * Voltage Monitor using IADC on EFR32xG22
 *
 * This project demonstrates how to use the window comparator feature in the
 * IADC as a voltage monitor peripheral in EFR32xG22.
 *
 * This project uses IADC to monitor four modes:
 * 1. Monitor AVDD with DCDC set to Bypass
 * 2. Monitor AVDD with DCDC enabled
 * 3. Monitor DVDD with DCDC set to Bypass
 * 4. Monitor DVDD with DCDC enabled
 *
 * Note: AVDD is shorted to VREGVDD on the starter kit. So AVDD can be treated
 *       as VREGVDD in this case.
 *
 * When DCDC is enabled, VREGVDD(AVDD in this case) should have its voltage
 * between 2.2-3.8V, the example uses those two values as the threshold.
 *
 * When DCDC is bypassed, AVDD should be in range of 1.8 to 3.8V,
 * this example uses 1.9 and 3.7 as the threshold to give early warnings.
 *
 * When DCDC is enabled, DVDD should be around 1.8V,
 * this example uses 1.755 and 1.86 as threshold.
 *
 * When DCDC is bypassed, DVDD should be the same as AVDD,
 * this example still uses 2.2-3.8V as the threshold.
 *
 * Board:  Silicon Labs EFR32xG22 Radio Board (BRD4182A) +
 *        Wireless Starter Kit Mainboard
 * Device: EFR32MG22C224F512IM40
******************************************************************************/
 
#include <stdio.h>
#include "em_device.h"
#include "em_chip.h"
#include "em_cmu.h"
#include "em_emu.h"
#include "em_iadc.h"
#include "em_gpio.h"
#include "bsp.h"
#include "display.h"
#include "retargettextdisplay.h"
#include "textdisplay.h"
#include "displayls013b7dh03.h"

/******************************************************************************
 *******************************   DEFINES   **********************************
 *****************************************************************************/

// Set CLK_ADC to 100kHz (this corresponds to a sample rate of 10ksps)
#define CLK_SRC_ADC_FREQ        10000000  // CLK_SRC_ADC
#define CLK_ADC_FREQ            100   // CLK_ADC

#define MENU_MAX			4

// Voltage selection enum
#define AVDD					0
#define AVDD_BYPASS	  1
#define DVDD_BYPASS	  2
#define DVDD					3

/******************************************************************************
 ***************************   GLOBAL VARIABLES   *****************************
 *****************************************************************************/

// Stores latest ADC sample
static volatile IADC_Result_t sample;

// test mode control signals
static volatile bool menuKey = false;
static volatile bool runKey = false;
static volatile bool detected_flag = false;
static volatile uint8_t menuLevel = 0;

// Upper and lower bound for Window Comparator
// 16-bit left-justified format; 12-bit conversion result compared to upper
// 12-bits of window comparator
static uint16_t avdd_lower_bound = 0x7550;	// 2.2V
static uint16_t avdd_upper_bound = 0xCAA0;	// 3.8V

static uint16_t avdd_lower_bound_bypass = 0x6550;		// 1.9V
static uint16_t avdd_upper_bound_bypass = 0xC540;		// 3.7V

static uint16_t dvdd_lower_bound = 0x5D90;	// 1.755V
static uint16_t dvdd_upper_bound = 0x6330;	// 1.86V

static uint16_t dvdd_lower_bound_bypass = 0x6550;		// 1.9V
static uint16_t dvdd_upper_bound_bypass = 0xC540;		// 3.7V

// voltage rail voltage range in mV
static volatile uint16_t dvdd_upper_voltage;
static volatile uint16_t dvdd_lower_voltage;

static volatile uint16_t avdd_upper_voltage;
static volatile uint16_t avdd_lower_voltage;

static volatile uint16_t avdd_upper_voltage_bypass;
static volatile uint16_t avdd_lower_voltage_bypass;

static volatile uint16_t dvdd_upper_voltage_bypass;
static volatile uint16_t dvdd_lower_voltage_bypass;

// measured voltage using ADC
static volatile uint16_t voltagemV;

/******************************************************************************
 * @brief Interrupt handler for push button BTN1.
 *****************************************************************************/
void GPIO_EVEN_IRQHandler(void)
{
  // Clear interrupt flags
  GPIO->IF_CLR = GPIO->IF;

  // If menuKey not pressed, increase menu
  if (!menuKey)
  {
    menuKey = true;
    if (++menuLevel == MENU_MAX)
    {
      menuLevel = 0;
    }
  }
}

/******************************************************************************
 * @brief Interrupt handler for push button BTN0.
 *****************************************************************************/
void GPIO_ODD_IRQHandler(void)
{
  GPIO->IF_CLR = GPIO->IF;
  runKey = true;
}

/**************************************************************************//**
 * @brief  GPIO Initializer
 *****************************************************************************/
void initGPIO (void)
{
  // Configure LED0 as output; enable WSTK LED0
  GPIO_PinModeSet(BSP_GPIO_LED0_PORT, BSP_GPIO_LED0_PIN, gpioModePushPull, 0);

  // Configure PB0 and PB1 as input
  GPIO_PinModeSet(BSP_GPIO_PB0_PORT, BSP_GPIO_PB0_PIN,
      gpioModeInputPullFilter, 1);
  GPIO_ExtIntConfig(BSP_GPIO_PB0_PORT, BSP_GPIO_PB0_PIN, BSP_GPIO_PB0_PIN,
      false, true, true);

  GPIO_PinModeSet(BSP_GPIO_PB1_PORT, BSP_GPIO_PB1_PIN,
      gpioModeInputPullFilter, 1);
  GPIO_ExtIntConfig(BSP_GPIO_PB1_PORT, BSP_GPIO_PB1_PIN, BSP_GPIO_PB1_PIN,
      false, true, true);

  // Enable falling edge detection interrupt for PB0 and PB1
  NVIC_ClearPendingIRQ(GPIO_EVEN_IRQn);
  NVIC_EnableIRQ(GPIO_EVEN_IRQn);

  NVIC_ClearPendingIRQ(GPIO_ODD_IRQn);
  NVIC_EnableIRQ(GPIO_ODD_IRQn);
}

/**************************************************************************//**
 * @brief  IADC Initializer
 *****************************************************************************/
void initIADC (IADC_PosInput_t posInput, IADC_NegInput_t negInput,
    uint16_t upperbound, uint16_t lowerbound)
{
  // Declare init structs
  IADC_Init_t init = IADC_INIT_DEFAULT;
  IADC_AllConfigs_t initAllConfigs = IADC_ALLCONFIGS_DEFAULT;
  IADC_InitSingle_t initSingle = IADC_INITSINGLE_DEFAULT;
  IADC_SingleInput_t initSingleInput = IADC_SINGLEINPUT_DEFAULT;

  // Reset IADC to reset configuration in case it has been modified
  IADC_reset(IADC0);

  // Configure IADC clock source to use the FSRCO(EM2/EM3)
  CMU_ClockSelectSet(cmuClock_IADCCLK, cmuSelect_FSRCO);

  // Modify init structs and initialize
  init.warmup = iadcWarmupKeepWarm;

  // Set the clk_src_adc prescale value here
  init.srcClkPrescale = IADC_calcSrcClkPrescale(IADC0, CLK_SRC_ADC_FREQ, 0);

  // Set upper bound for window compare
  init.greaterThanEqualThres = upperbound;

  // Set lower bound for window compare
  init.lessThanEqualThres = lowerbound;

  // Configuration 0 is used by both scan and single conversions by default
  // Use internal 1.2V bandgap as reference
  initAllConfigs.configs[0].reference = iadcCfgReferenceInt1V2;

  // Divides CLK_SRC_ADC to set the CLK_ADC frequency for desired sample rate
  initAllConfigs.configs[0].adcClkPrescale = IADC_calcAdcClkPrescale(IADC0,
                                                              CLK_ADC_FREQ,
                                                                         0,
                                                         iadcCfgModeNormal,
                                                       init.srcClkPrescale);

  // Set conversions to run continuously
  initSingle.triggerAction = iadcTriggerActionContinuous;

  // Configure Input sources for single ended conversion
  initSingleInput.posInput = posInput;
  initSingleInput.negInput = negInput;

  // Enable window comparisons on this input
  initSingleInput.compare = true;

  // Initialize IADC
  IADC_init(IADC0, &init, &initAllConfigs);

  // Initialize Scan
  IADC_initSingle(IADC0, &initSingle, &initSingleInput);

  // Enable interrupts on window comparison match
  IADC_enableInt(IADC0, IADC_IF_SINGLECMP);

  // Enable ADC interrupts
  NVIC_ClearPendingIRQ(IADC_IRQn);
  NVIC_EnableIRQ(IADC_IRQn);
}

/**************************************************************************//**
 * @brief  ADC Handler
 *****************************************************************************/
void IADC_IRQHandler(void)
{
  IADC_clearInt(IADC0, IADC_IF_SINGLECMP);

  // Read most recent sample
  sample = IADC_readSingleResult(IADC0);
  voltagemV = (sample.data)*4*1200/4095;

  // stop ADC conversion, set detected flag to true
  IADC_command(IADC0, iadcCmdStopSingle);
  detected_flag = true;
}

/*****************************************************************************/
/*@brief: Starter kit LCD display, select voltage
 * to be monitored
 *****************************************************************************/
void displaySelection(void){

	// Welcome Message
	printf("EFR32xG22 VMON\nwith IADC\nSoftware Example\n\n\n");
	printf("Press Push Button 0\nto view test modes");
}

/*****************************************************************************/
/*@brief: enable clock branches
 *****************************************************************************/
void initCMU(void){
	CMU_ClockEnable(cmuClock_IADC0, true);
	CMU_ClockEnable(cmuClock_GPIO, true);
}

/*****************************************************************************/
/*@brief: initialize global variables
 *****************************************************************************/
void initVariable(void){

  // AVDD upper and lower voltage threshold in mV with DCDC regulator on
  avdd_upper_voltage = (avdd_upper_bound>>4)*1200*4/4095;
  avdd_lower_voltage = (avdd_lower_bound>>4)*1200*4/4095;

  // DVDD upper and lower voltage threshold in mV with DCDC regulator on
  dvdd_upper_voltage = (dvdd_upper_bound>>4)*1200*4/4095;
  dvdd_lower_voltage = (dvdd_lower_bound>>4)*1200*4/4095;

  // AVDD upper and lower voltage threshold in mV with DCDC regulator bypassed
  avdd_upper_voltage_bypass = (avdd_upper_bound_bypass>>4)*1200*4/4095;
  avdd_lower_voltage_bypass = (avdd_lower_bound_bypass>>4)*1200*4/4095;

  // DVDD upper and lower voltage threshold in mV with DCDC regulator bypassed
  dvdd_upper_voltage_bypass = (dvdd_upper_bound_bypass>>4)*1200*4/4095;
  dvdd_lower_voltage_bypass = (dvdd_lower_bound_bypass>>4)*1200*4/4095;
}

/**************************************************************************//**
 * @brief  Main function
 *****************************************************************************/
int main(void)
{
  CHIP_Init();

  // DCDC Initialization
  EMU_DCDCInit_TypeDef dcdcInit = EMU_DCDCINIT_DEFAULT;

  // DCDC startup voltage at 2.2V
  dcdcInit.cmpThreshold = emuVreginCmpThreshold_2v2;

  // initialize peripheral clocks
  initCMU();

  // Initialize GPIO
  initGPIO();

  // Initialize Display
  DISPLAY_Init();

  /* Retarget stdio to a text display. */
  if (RETARGET_TextDisplayInit() != TEXTDISPLAY_EMSTATUS_OK)
  {
    /* Text display initialization failed. */
    while (1);
  }

  // Display welcome screen
  displaySelection();

  // initialize global variables
  initVariable();

  // Infinite loop
  while(1){
	  // if PB0 pressed, navigate through different mode selection
	  if(menuKey){

	    switch (menuLevel){

	      case AVDD:
	        IADC_reset(IADC0);
	        printf("\f");
	        printf("AVDD/VREGVDD\nVoltage Monitoring\nDCDC Enabled\n\n");
	        printf("Upper Voltage Bound: %dmV\n", avdd_upper_voltage);
	        printf("Lower Voltage Bound: %dmV\n\n", avdd_lower_voltage);
	        printf("Press PB1 to run\n\n");
	        break;
	      case DVDD:
	        IADC_reset(IADC0);
	        printf("\f");
	        printf("DVDD Voltage\nMonitoring\nDCDC Enabled\n\n");
	        printf("DVDD Upper Bound:\n%dmV\n",dvdd_upper_voltage);
	        printf("DVDD Lower Bound:\n%dmV\n",dvdd_lower_voltage);
	        printf("Press PB1 to run\n\n");
	        break;
	      case AVDD_BYPASS:
	        IADC_reset(IADC0);
	        printf("\f");
	        printf("AVDD/VREGVDD\nVoltage Monitoring\nDCDC Bypassed\n\n");
	        printf("Upper Voltage Bound: %dmV\n", avdd_upper_voltage_bypass);
	        printf("Lower Voltage Bound: %dmV\n", avdd_lower_voltage_bypass);
	        printf("Press PB1 to run\n\n");
	        break;
	      case DVDD_BYPASS:
	        IADC_reset(IADC0);
	        printf("\f");
	        printf("DVDD Voltage\nMonitoring\nDCDC Bypassed\n\n");
	        printf("DVDD Upper Bound:\n%dmV\n",dvdd_upper_voltage_bypass);
	        printf("DVDD Lower Bound:\n%dmV\n",dvdd_lower_voltage_bypass);
	        printf("Press PB1 to run\n\n");
	        break;
	      default:
	        break;
	    }
	    // set menuKey flag to false
	    menuKey = false;
	  }

	  // if PB1 pressed, run the selected mode
    if(runKey){

      switch (menuLevel){

        case AVDD:
          dcdcInit.mode = emuDcdcMode_Regulation;
          printf("\f");
          printf("Starting DCDC\n"
                 "Please raise input voltage to at least"
                 " 2.2V to startup the DCDC");
          EMU_DCDCInit(&dcdcInit);	// initialize dcdc with regulator on
          initIADC(iadcPosInputAvdd,iadcNegInputGnd,
                   avdd_upper_bound,avdd_lower_bound);
          IADC_command(IADC0, iadcCmdStartSingle);	// Start scan
          printf("\f");
          printf("running AVDD\n\nVoltage Monitor\n\nwith DCDC Enabled\n");
          break;
        case DVDD:
          dcdcInit.mode = emuDcdcMode_Regulation;
          printf("\f");
          printf("Starting DCDC\nPlease raise input voltage to at least"
                 " 2.2V to startup the DCDC");
          EMU_DCDCInit(&dcdcInit);	// initialize dcdc with regulator on
          initIADC(iadcPosInputDvdd,iadcNegInputGnd,
                   dvdd_upper_bound,dvdd_lower_bound);
          IADC_command(IADC0, iadcCmdStartSingle);	// Start scan
          printf("\f");
          printf("running DVDD\n\nVoltage Monitor\n\nwith DCDC Enabled\n");
          break;
        case AVDD_BYPASS:
          dcdcInit.mode = emuDcdcMode_Bypass;	// initialize dcdc in bypass mode
          EMU_DCDCInit(&dcdcInit);
          initIADC(iadcPosInputAvdd,iadcNegInputGnd,
                   avdd_upper_bound_bypass,avdd_lower_bound_bypass);
          IADC_command(IADC0, iadcCmdStartSingle);	// Start scan
          printf("\f");
          printf("running AVDD\n\nVoltage Monitor\n\nwith DCDC Bypassed\n");
          break;
        case DVDD_BYPASS:
          dcdcInit.mode = emuDcdcMode_Bypass;	// initialize dcdc in bypass mode
          EMU_DCDCInit(&dcdcInit);
          initIADC(iadcPosInputDvdd,iadcNegInputGnd,
                   dvdd_upper_bound_bypass,dvdd_lower_bound_bypass);
          IADC_command(IADC0, iadcCmdStartSingle);	// Start scan
          printf("\f");
          printf("running DVDD\n\nVoltage Monitor\n\nwith DCDC Bypassed\n");
          break;
        default:
          break;
      }
        runKey = false;
    }

    // if compare mode triggered, update display,
    // power off DCDC if appropriate.
    if(detected_flag){

    	switch(menuLevel){

    	  case AVDD:
    	    EMU_DCDCPowerOff();
    	    printf("\f");
    	    printf("DCDC enabled\n");
    	    printf("AVDD voltage out of\nrange\n");
    	    printf("Expected Lower Voltage: %dmV\n", avdd_lower_voltage);
    	    printf("Expected Upper Voltage: %dmV\n", avdd_upper_voltage);
    	    printf("Voltage measured: %dmV\n\n", voltagemV);
    	    printf("Disabling DCDC\n");
    	    printf("Press PB0 to select mode\nor PB1 to run this mode again\n");
    	    break;
    	  case AVDD_BYPASS:
    	    printf("\f");
    	    printf("DCDC Bypassed\n");
    	    printf("AVDD voltage out of\nrange\n");
    	    printf("Expected Lower Voltage: %dmV\n", avdd_lower_voltage_bypass);
    	    printf("Expected Upper Voltage: %dmV\n", avdd_upper_voltage_bypass);
    	    printf("Voltage measured: %dmV\n\n", voltagemV);
    	    printf("Press PB0 to select mode or PB1 to run this mode again\n");
    	    break;
    	  case DVDD:
    	    printf("\f");
    	    printf("DCDC enabled\n");
    	    printf("DVDD voltage out of\nrange\n");
    	    printf("Expected Lower Voltage: %dmV\n", dvdd_lower_voltage);
    	    printf("Expected Upper Voltage: %dmV\n", dvdd_upper_voltage);
    	    printf("Voltage measured:\n%dmV\n\n", voltagemV);
    	    printf("Press PB0 to select mode or PB1 to run this mode again\n");
    	    break;
    	  case DVDD_BYPASS:
    	    printf("\f");
    	    printf("DCDC Bypassed\n");
    	    printf("DVDD voltage out of\nrange\n");
    	    printf("Expected Lower Voltage: %dmV\n", dvdd_lower_voltage_bypass);
    	    printf("Expected Upper Voltage: %dmV\n", dvdd_upper_voltage_bypass);
    	    printf("Voltage measured:\n%dmV\n\n", voltagemV);
    	    printf("Press PB0 to select mode or PB1 to run this mode again\n");
    	    break;
    	  default:
    	    break;
    	}
    	detected_flag = false;
    }

    // Enter EM3, wait for next interrupt
    EMU_EnterEM3(true);
  }
}
