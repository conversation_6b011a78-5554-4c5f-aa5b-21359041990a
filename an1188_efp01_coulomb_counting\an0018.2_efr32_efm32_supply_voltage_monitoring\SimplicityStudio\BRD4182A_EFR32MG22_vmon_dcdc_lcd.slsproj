<?xml version="1.0" encoding="UTF-8"?>
<project name="BRD4182A_EFR32MG22_vmon_dcdc_lcd" boardCompatibility="brd4182a" partCompatibility=".*efr32mg22c224f512im40.*" toolchainCompatibility="" contentRoot="../">
  <module id="com.silabs.sdk.exx32.board">
    <exclude pattern=".*" />
  </module>
  <module id="com.silabs.sdk.exx32.common.CMSIS">
    <exclude pattern=".*" />
  </module>
  <module id="com.silabs.sdk.exx32.common.emlib">
    <include pattern="emlib/em_assert.c" />
    <include pattern="emlib/em_cmu.c" />
    <include pattern="emlib/em_core.c" />
    <include pattern="emlib/em_emu.c" />
    <include pattern="emlib/em_gpio.c" />
    <include pattern="emlib/em_prs.c" />
    <include pattern="emlib/em_rtcc.c" />
    <include pattern="emlib/em_system.c" />
    <include pattern="emlib/em_usart.c" />
    <include pattern="emlib/em_rmu.c" />
    <include pattern="emlib/em_iadc.c" />
  </module>
  <module id="com.silabs.sdk.exx32.common.bsp">
    <exclude pattern=".*" />
  </module>
  <module id="com.silabs.sdk.exx32.common.drivers">
    <include pattern="Drivers/display.c" />
    <include pattern="Drivers/displayls013b7dh03.c" />
    <include pattern="Drivers/displaypalemlib.c" />
    <include pattern="Drivers/mx25flash_spi.c" />
    <include pattern="Drivers/retargetio.c" />
    <include pattern="Drivers/retargettextdisplay.c" />
    <include pattern="Drivers/textdisplay.c" />
    <include pattern="Drivers/udelay.c" />
  </module>
  <module id="com.silabs.sdk.exx32.part">
    <include pattern="CMSIS/.*/startup_.*_.*.s" />
    <include pattern="CMSIS/.*/system_.*.c" />
  </module>
  <module id="com.silabs.sdk.exx32.external.glib">
    <exclude pattern=".*" />
  </module>
  <macroDefinition name="DEBUG_EFM" languageCompatibility="c cpp" />
  <macroDefinition name="RETARGET_VCOM" value="1" />
  <includePath uri="src" />
  <folder name="src">
    <file name="main_vmon_dcdc_lcd.c" uri="src/main_vmon_dcdc_lcd.c" />
    <file name="displayconfigapp.h" uri="src/displayconfigapp.h" />
  </folder>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.flags" value="-c -x assembler-with-cpp -mfloat-abi=softfp -mfpu=fpv4-sp-d16 "/>
</project>