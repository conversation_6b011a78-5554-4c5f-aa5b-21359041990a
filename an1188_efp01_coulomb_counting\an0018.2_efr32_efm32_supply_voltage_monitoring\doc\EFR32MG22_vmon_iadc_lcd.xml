<?xml version='1.0' encoding='utf-8'?>
<workspace name="an0018_supply_voltage_monitoring">
  <project device="EFR32MG22C224F512IM40"
           name="EFR32MG22_vmon_iadc_lcd">
    <targets>
      <name>iar</name>
      <name>slsproj</name>
    </targets>
    <directories>
      <cmsis>$PROJ_DIR$\..\..\..\platform</cmsis>
      <device>$PROJ_DIR$\..\..\..\platform\Device\SiliconLabs</device>
      <emlib>$PROJ_DIR$\..\..\..\platform\emlib</emlib>
      <drivers>$PROJ_DIR$\..\..\..\hardware\kit\common\drivers</drivers>
      <bsp>$PROJ_DIR$\..\..\..\hardware\kit\common\bsp</bsp>
      <kitconfig>$PROJ_DIR$\..\..\..\hardware\kit\EFR32MG22_BRD4182A\config</kitconfig>
    </directories>
    <includepaths>
	  <path>##em-path-cmsis##\CMSIS\Include</path>
      <path>##em-path-device##\EFR32MG22\Include</path>
      <path>##em-path-emlib##\inc</path>
      <path>##em-path-kitconfig##</path>
      <path>##em-path-bsp##</path>
      <path>##em-path-drivers##</path>
      <path>##em-path-cmsis##\middleware\glib</path>
      <path>$PROJ_DIR$\..\src</path>
    </includepaths>
    <group name="CMSIS">
      <source>##em-path-device##\EFR32MG22\Source\$IDE$\startup_efr32mg22.s</source>
      <source>##em-path-device##\EFR32MG22\Source\system_efr32mg22.c</source>
    </group>
    <group name="drivers">
      <source>##em-path-drivers##\display.c</source>
      <source>##em-path-drivers##\displayls013b7dh03.c</source>
      <source>##em-path-drivers##\displaypalemlib.c</source>
	  <source>##em-path-drivers##\mx25flash_spi.c</source>
      <source>##em-path-drivers##\retargetio.c</source>
      <source>##em-path-drivers##\retargettextdisplay.c</source>
      <source>##em-path-drivers##\textdisplay.c</source>
      <source>##em-path-drivers##\udelay.c</source>
    </group>
    <group name="emlib">
      <source>##em-path-cmsis##\emlib\src\em_assert.c</source>
      <source>##em-path-cmsis##\emlib\src\em_cmu.c</source>
      <source>##em-path-cmsis##\emlib\src\em_core.c</source>
      <source>##em-path-cmsis##\emlib\src\em_emu.c</source>
      <source>##em-path-cmsis##\emlib\src\em_gpio.c</source>
      <source>##em-path-cmsis##\emlib\src\em_prs.c</source>
      <source>##em-path-cmsis##\emlib\src\em_rtcc.c</source>
      <source>##em-path-cmsis##\emlib\src\em_system.c</source>
      <source>##em-path-cmsis##\emlib\src\em_usart.c</source>
      <source>##em-path-cmsis##\emlib\src\em_rmu.c</source>
      <source>##em-path-cmsis##\emlib\src\em_iadc.c</source>
    </group>
    <group name="Source">
      <source>$PROJ_DIR$\..\src\main_vmon_iadc_xg22.c</source>
      <source>$PROJ_DIR$\..\src\displayconfigapp.h</source>
    </group>
	<cflags>
      <define>DEBUG_EFM</define>
      <define>RETARGET_VCOM=1</define>
      <diagsuppress only_ide="iar">Pa050</diagsuppress>
    </cflags>
  </project>
</workspace>
