/**************************************************************************//**
 * @file
 * @brief This project demonstrates the DCDC VREGVDD Threshold Comparator
 *        The device will be switching between DCDC regulator on and DCDC
 *        bypass mode depending on the input voltage to VREGVDD. Follow
 *        the LCD for testing instruction
 * @version  1.10

 ******************************************************************************
 * @section License
 * <b>(C) Copyright 2020 Silicon Labs, http://www.silabs.com</b>
 ******************************************************************************
 *
 * This file is licensed under the Silicon Labs Software License Agreement. See
 * "http://developer.silabs.com/legal/version/v11/
 * Silicon_Labs_Software_License_Agreement.txt"
 * for details. Before using this software for any purpose, you must agree to
 * the terms of that agreement.
 *
 *****************************************************************************/

/*************************************************************************//**
 * vmon_dcdc_lcd
 *
 * This project demonstrates the DCDC VVREGVDD Threshold Comparator. The device
 * will be switching between DCDC regulator on and DCDC regulator bypass mode
 * depending on the input voltage to VREGVDD. The LCD will display the DCDC
 * modes.
 *
 * To test:
 * 1. Compile the project in Simplicity Studio and
 *    flash the hex image onto the device.
 * 2. Place power switch at BAT and supply voltage across VMCU and GND
 *    (pin 1 and 2 on the breakout pad).
 * 3. Follow the LCD instructions to test.
 *
 * Board:  Silicon Labs EFR32xG22 Radio Board (BRD4182A) +
 *         Wireless Starter Kit Mainboard
 * Device: EFR32MG22C224F512IM40
******************************************************************************/

#include <stdio.h>
#include "em_device.h"
#include "em_chip.h"
#include "em_cmu.h"
#include "em_emu.h"
#include "em_gpio.h"
#include "bsp.h"
#include "display.h"
#include "retargettextdisplay.h"
#include "textdisplay.h"
#include "displayls013b7dh03.h"

/******************************************************************************
 ***************************   GLOBAL VARIABLES   *****************************
 *****************************************************************************/

// test mode control signals
volatile bool low_voltage = false;

/******************************************************************************
 * @brief Interrupt handler for push button BTN1.
 *****************************************************************************/
void GPIO_EVEN_IRQHandler(void)
{
  GPIO->IF_CLR = GPIO->IF;
}

/**************************************************************************//**
 * @brief  GPIO Initializer
 *****************************************************************************/
void initGPIO (void)
{
  // Configure push button 0 as input, enable edge detection interrupt
  GPIO_PinModeSet(BSP_GPIO_PB0_PORT, BSP_GPIO_PB0_PIN,
      gpioModeInputPullFilter, 1);
  GPIO_ExtIntConfig(BSP_GPIO_PB0_PORT, BSP_GPIO_PB0_PIN,
      BSP_GPIO_PB0_PIN, false, true, true);

  NVIC_ClearPendingIRQ(GPIO_EVEN_IRQn);
  NVIC_EnableIRQ(GPIO_EVEN_IRQn);
}

/*****************************************************************************/
/* @brief: Starter kit LCD display, select voltage
 * to be monitored
 *****************************************************************************/
void displaySelection(void){

	// Welcome Message
	printf("EFR32xG22 VMON\nwith DCDC\nBuilt in Comparator"
	    "\nSoftware Example\n\n\n");
}

/*****************************************************************************/
/*@brief: enable clock branches
 *****************************************************************************/
void initCMU(void){
	// Disable all low frequency clock except LFXO to save power
	CMU_OscillatorEnable(cmuOsc_ULFRCO,false,true);
	CMU_OscillatorEnable(cmuOsc_LFXO, true, true);
	CMU_OscillatorEnable(cmuOsc_LFRCO,false,true);
	CMU_OscillatorEnable(cmuOsc_FSRCO,false,true);

	// Select all low frequency clock to LFXO
	CMU_ClockSelectSet(cmuClock_EM23GRPACLK, cmuSelect_LFXO);;
	CMU_ClockSelectSet(cmuClock_WDOG0CLK, cmuSelect_LFXO);
	CMU_ClockSelectSet(cmuClock_RTCCCLK, cmuSelect_LFXO);

	// Enable GPIO clock
	CMU_ClockEnable(cmuClock_GPIO, true);
}

/*****************************************************************************/
/*@brief: DCDC interrupt handler
 *****************************************************************************/
void DCDC_IRQHandler(void){
	volatile uint32_t flag = DCDC -> IF;

	// Clear all interrupt flag
	DCDC -> IF_CLR = flag;

	// Check for interrupt flag
	if(flag & DCDC_IEN_VREGINLOW){
		low_voltage = true;
		DCDC -> IEN_CLR |= DCDC_IEN_VREGINLOW;
	}
	if(flag & DCDC_IEN_VREGINHIGH){
		low_voltage = false;
		DCDC -> IEN_CLR |= DCDC_IEN_VREGINHIGH;
	}
}

/*****************************************************************************/
/*@brief DCDC Initialization
 *****************************************************************************/
void initDCDC(void){
	EMU_DCDCInit_TypeDef dcdcInit = EMU_DCDCINIT_DEFAULT;

	// Change dcdc threshold voltage level to 2.2V
	dcdcInit.cmpThreshold = emuVreginCmpThreshold_2v2;
	printf("Please make sure your\nsupply voltage to\n"
	    "VREGDD is greater\nthan 2.2V.\nPress PB0 to\nrun the example\n");

	// Enter EM3 while waiting for user input
	EMU_EnterEM3(true);

	printf("\f");
	printf("DCDC Initializing\n\nRaise VREGVDD above\n2.2V to start DCDC");

	// Initializing DCDC with regulator on
	EMU_DCDCInit(&dcdcInit);

	// Initialization success message
	printf("\f");
	printf("DCDC initialization\nsuccessful, running\n"
	    "with regulator enabled.\n\n");
	printf("Decrease VREGVDD\nsupply to below 2.2V\nto enter bypass mode\n");

	// Clear all DCDC interrupt
	DCDC->IF_CLR |= _DCDC_IF_MASK;

	// Enable VREGIN low detection interrupt
	DCDC->IEN_SET |= DCDC_IEN_VREGINLOW;

	// Enable DCDC interrupt vector.
	NVIC_ClearPendingIRQ(DCDC_IRQn);
	NVIC_EnableIRQ(DCDC_IRQn);
}

/*****************************************************************************/
/*@brief: EMU Init routine, initialize EM23 with default settings
 *****************************************************************************/
void initEMU(void){
  EMU_EM23Init_TypeDef em23Init = EMU_EM23INIT_DEFAULT;
  EMU_EM23Init(&em23Init);
}

/**************************************************************************//**
 * @brief  Main function
 *****************************************************************************/
int main(void)
{
  CHIP_Init();

  // initialize peripheral clocks
  initCMU();

  // Initialize Display
  DISPLAY_Init();

  /* Retarget stdio to a text display. */
  if (RETARGET_TextDisplayInit() != TEXTDISPLAY_EMSTATUS_OK)
  {
    /* Text display initialization failed. */
    while (1);
  }

  // Display welcome screen
  displaySelection();

  // Initialize GPIO
  initGPIO();

  // Initialize EM2/3 with default settings
  initEMU();

  // Initialize DCDC
  initDCDC();

  // Disable GPIO interrupt
  GPIO_ExtIntConfig(BSP_GPIO_PB0_PORT, BSP_GPIO_PB0_PIN, BSP_GPIO_PB0_PIN,
      false, true, false);

  // Infinite loop
  while(1){

    // Enter EM1 while waiting for threshold detection
    EMU_EnterEM1();

    // If below threshold, switch to bypass mode
    // Enable interrupt flags to detect VREGIN > VTHRESHOLD
    if(low_voltage){
      printf("\f");
      printf("VREGVDD below\nthreshold voltage\nswitching to bypass\nmode\n"
          "\n");
      EMU_DCDCModeSet(emuDcdcMode_Bypass);
      printf("bypass mode enabled. Raise VREGVDD to\n2.2V to enable DCDC");
      DCDC -> IEN_SET |= DCDC_IEN_VREGINHIGH;
    }

	  // If above threshold, switch to regulator on mode
    // Enable interrupt flags to detect VREGIN < VTHRESHOLD
    else{
      printf("\f");
      EMU_DCDCModeSet(emuDcdcMode_Regulation);
      printf("DCDC enabled\n\nDecrease VREGVDD\nsupply to below 2.2V\n"
             "to enter bypass mode\n");
      DCDC -> IEN_SET |= DCDC_IEN_VREGINLOW;
    }
  }
}
