<?xml version="1.0" encoding="UTF-8"?>
<project name="BRD4179B_EFR32MG21_EFP01_Coulomb_Counting" boardCompatibility="brd4179b" partCompatibility=".*efr32mg21a010f1024im32.*" toolchainCompatibility="" contentRoot="../">
  <module id="com.silabs.sdk.exx32.board">
    <exclude pattern=".*" />
  </module>
  <module id="com.silabs.sdk.exx32.common.CMSIS">
    <exclude pattern=".*" />
  </module>
  <module id="com.silabs.sdk.exx32.common.emlib">
    <include pattern="emlib/em_assert.c" />
    <include pattern="emlib/em_cmu.c" />
    <include pattern="emlib/em_core.c" />
    <include pattern="emlib/em_gpio.c" />
    <include pattern="emlib/em_system.c" />
    <include pattern="emlib/em_usart.c" />
    <include pattern="emlib/em_i2c.c" />
  </module>
  <module id="com.silabs.sdk.exx32.common.bsp">
    <exclude pattern=".*" />
  </module>
  <module id="com.silabs.sdk.exx32.common.drivers">
    <include pattern="Drivers/retargetio.c" />
    <include pattern="Drivers/retargetserial.c" />
  </module>
  <module id="com.silabs.sdk.exx32.part">
    <include pattern="CMSIS/.*/startup_.*_.*.s" />
    <include pattern="CMSIS/.*/system_.*.c" />
  </module>
  <macroDefinition name="DEBUG_EFM" languageCompatibility="c cpp" />
  <macroDefinition name="RETARGET_VCOM" value="1" />
  <includePath uri="../../hardware/driver/efp/inc" />
  <includePath uri="../../platform/common/inc" />
  <includePath uri="../../platform/driver/i2cspm/inc" />
  <includePath uri="../../util/third_party/crypto/sl_component/se_manager/inc" />
  <folder name="Drivers">
    <file name="sl_i2cspm.c" uri="../../platform/driver/i2cspm/src/sl_i2cspm.c" />
    <file name="sl_se_manager.c" uri="../../util/third_party/crypto/sl_component/se_manager/src/sl_se_manager.c" />
  </folder>
  <folder name="efp">
    <file name="sl_efp.c" uri="../../hardware/driver/efp/src/sl_efp.c" />
  </folder>
  <folder name="src">
    <file name="main.c" uri="src/main.c" />
    <file name="sl_efp_inst_config.h" uri="src/sl_efp_inst_config.h" />
    <file name="readme.txt" uri="src/readme.txt" />
  </folder>
  <libraryFile name="m" toolchainCompatibility="com.silabs.ss.tool.ide.arm.toolchain.gnu.*" />
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.flags" value="-c -x assembler-with-cpp -mfloat-abi=softfp -mfpu=fpv4-sp-d16 "/>
</project>