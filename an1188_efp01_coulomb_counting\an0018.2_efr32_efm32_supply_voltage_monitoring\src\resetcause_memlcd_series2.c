/**************************************************************************//**
 * @file resetcause_memlcd_series2.c
 * @brief RMU Demo Application
 * <AUTHOR> Labs
 * @version  1.10

 ******************************************************************************
 * @section License
 * <b>(C) Copyright 2014 Silicon Labs, http://www.silabs.com</b>
 ******************************************************************************
 *
 * Permission is granted to anyone to use this software for any purpose,
 * including commercial applications, and to alter it and redistribute it
 * freely, subject to the following restrictions:
 *
 * 1. The origin of this software must not be misrepresented; you must not
 *    claim that you wrote the original software.
 * 2. Altered source versions must be plainly marked as such, and must not be
 *    misrepresented as being the original software.
 * 3. This notice may not be removed or altered from any source distribution.
 *
 * DISCLAIMER OF WARRANTY/LIMITATION OF REMEDIES: Silicon Labs has no
 * obligation to support this Software. Silicon Labs is providing the
 * Software "AS IS", with no express or implied warranties of any kind,
 * including, but not limited to, any implied warranties of merchantability
 * or fitness for any particular purpose or warranties against infringement
 * of any proprietary rights of a third party.
 *
 * Silicon Labs will not be liable for any consequential, incidental, or
 * special damages, or any other relief, or for any claim by any third party,
 * arising from your use of this Software.
 *
 *****************************************************************************/

 /****************************************************************************
 * This project demonstrates how to read the RMU's reset cause register to
 * determine the cause of the device's last reset.
 * In this example, the reset cause register is read
 * and stored in a global variable and the reset cause register status is
 * cleared. Additional reset sources that can be disabled are enabled.
 * Then the WSTK onboard LCD is initialized and the reset cause
 * is displayed on the LCD.
 *
 * How to Test:
 * 1.  Update the kit's firmware from the Simplicity Launcher (if necessary)
 * 2.  Build the project and download to the Starter Kit
 * 3.  Disconnect Simplicity Studio debugger from the starter kit
 *     (just click disconnect button in Simplicity Studio)
 * 4.  Place the Starter Kit power switch into BAT
 *     to disconnect the debugger's power
 * 5.  Attach a positive 3.30V power supply to the GND and MCU pins of the
 *     WSTK Expansion Header;
 *     Positive 3.30V should be applied to EXP pin 2 and 0.0V applied to EXP
 *     pin 1
 * 6.  Disconnect and reconnect external power supply
 * 7.  Observe the reset causes displayed on the WSTK LCD screen
 * 8.  Press the reset push button on the WSTK
 * 9.  Observe the reset cause displayed on the WSTK LCD screen
 *
 * If the LCD fails to initialize, PB01 (EXP pin 13) will go high and turn on
 * LED1 on the WSTK as an indicator
 *
 * Peripherals Used:
 * Display Module and stdout retarget to TEXTDISPLAY device
 * RMU     - Enabled soft reset sources:
 *      WDOG0
 *		  WDOG1 (if applicable)
 *		  LOCKUP
 *		  SYSREQ
 *		  AVDDBOD
 *		  VDDIO0BOD
 *		  SESYSREQ (if applicable)
 *		  SELOCKUP (if applicable)
 *    - NOTE: wdog resets won't trigger without setting up WDOG timers;
 *            see AN0015.2 for WDOG timer example code
 *
 * Board:  Silicon Labs EFR32xG21 Radio Board (BRD4181A) +
 *       Wireless Starter Kit Mainboard
 * Device: EFR32MG21A010F1024IM32
 * PB00 - GPIO Push/Pull output, Expansion Header Pin 11, WSTK Pin 8, LED0
 *
 * Board:  Silicon Labs EFR32xG22 Radio Board (BRD4182A) +
 *       Wireless Starter Kit Mainboard
 * Device: EFR32MG22C224F512IM40
 * PD02 -  GPIO Push/Pull output, Expansion Header Pin 11, WSTK Pin 8, LED0
******************************************************************************/

#include "em_device.h"
#include "em_chip.h"
#include "em_emu.h"
#include "em_cmu.h"
#include "display.h"
#include "stdio.h"
#include "textdisplay.h"
#include "retargettextdisplay.h"
#include "em_rmu.h"

uint32_t resetCause;

#if defined EFR32MG21A010F1024IM32
  #define BSP_LED0_PORT gpioPortB
  #define BSP_LED0_PIN 0
#elif defined EFR32MG22C224F512IM40
  #define BSP_LED0_PORT gpioPortD
  #define BSP_LED0_PIN 2
#endif

/**************************************************************************//**
 * @brief
 *   Displays reset cause on WSTK LCD
 *
 *   Certain resets can trigger multiple reset cause bits to set; this function
 *   prioritizes the causes and returns actual reset cause in accordance to
 *   AN0018.2 when multiple bits are set.
 *
 *****************************************************************************/
void DisplayResetCause()
{
  if (resetCause & EMU_RSTCAUSE_POR)
    printf("\nPOR");
  else if (resetCause & EMU_RSTCAUSE_PIN)
    printf("\nPIN");
  else if (resetCause & EMU_RSTCAUSE_EM4)
    printf("\nEM4");
  else if (resetCause & EMU_RSTCAUSE_WDOG0)
    printf("\nWDOG0");
#if defined EFR32MG21A010F1024IM32
  else if (resetCause & EMU_RSTCAUSE_WDOG1)
    printf("\nWDOG1");
#endif
  else if (resetCause & EMU_RSTCAUSE_LOCKUP)
    printf("\nLOCKUP");
  else if (resetCause & EMU_RSTCAUSE_SYSREQ)
    printf("\nSYSREQ");
  else if (resetCause & EMU_RSTCAUSE_DVDDBOD)
    printf("\nDVDDBOD");
  else if (resetCause & EMU_RSTCAUSE_DVDDLEBOD)
    printf("\nDVDDDLEBOD");
  else if (resetCause & EMU_RSTCAUSE_DECBOD)
    printf("\nDECBOD");
  else if (resetCause & EMU_RSTCAUSE_AVDDBOD)
    printf("\nAVDDBOD");
  else if (resetCause & EMU_RSTCAUSE_IOVDD0BOD)
    printf("\nIOVDD0BOD");
#if defined EFR32MG21A010F1024IM32
  else if (resetCause & EMU_RSTCAUSE_SETAMPER)
    printf("\nSETAMPER");
  else if (resetCause & EMU_RSTCAUSE_SESYSREQ)
    printf("\nSESYSREQ");
  else if (resetCause & EMU_RSTCAUSE_SELOCKUP)
    printf("\nSELOCKUP");
#elif defined EFR32MG22C224F512IM40
  else if (resetCause & EMU_RSTCAUSE_DCI)
    printf("\nDCI");
  else if (resetCause & EMU_RSTCAUSE_VREGIN)
    printf("\nVREGIN");
#endif
}

/**************************************************************************//**
 * @brief
 *   Main function
 *
 * @details
 *   Reads the RESETCAUSE register and displays cause of reset on WSTK LCD
 *
 *****************************************************************************/
int main()
{
  /* Chip errata */
  CHIP_Init();

  /* Read and clear reset cause register */
  resetCause = RMU_ResetCauseGet();
  if (resetCause != 0x0)
    RMU_ResetCauseClear();

  /* Enable all reset peripherals */
  RMU_ResetControl(rmuResetWdog0, rmuResetModeEnabled);
#if defined EFR32MG21A010F1024IM32
  RMU_ResetControl(rmuResetWdog1, rmuResetModeEnabled);
#endif
  RMU_ResetControl(rmuResetSys, rmuResetModeEnabled);
  RMU_ResetControl(rmuResetCoreLockup, rmuResetModeEnabled);
  RMU_ResetControl(rmuResetAVDD, rmuResetModeEnabled);
  RMU_ResetControl(rmuResetIOVDD0, rmuResetModeEnabled);
  RMU_ResetControl(rmuResetDecouple, rmuResetModeEnabled);
#if defined EFR32MG21A010F1024IM32
  RMU_ResetControl(rmuResetSESys, rmuResetModeEnabled);
  RMU_ResetControl(rmuResetSELockup, rmuResetModeEnabled);
#endif

  /* Enable GPIO Clock */
  CMU_ClockEnable(cmuClock_GPIO, true);

  /* Initialize LCD and display reset cause */
  /* Initialize the STK's LCD */
  DISPLAY_Init();

  /* Retarget stdio to a text display. */
  if (RETARGET_TextDisplayInit() != TEXTDISPLAY_EMSTATUS_OK)
  {
    /* Text display initialization failed. */
    GPIO_PinModeSet(BSP_LED0_PORT, BSP_LED0_PIN, gpioModePushPull, 1);
    while (1);
  }

  DisplayResetCause();

  while (1)
  {
    EMU_EnterEM2(false);
  }
}
