<?xml version='1.0' encoding='utf-8'?>
<workspace name="an0018_supply_voltage_monitoring">
  <project device="EFR32MG21A010F1024IM32"
           name="EFR32MG21_vmon_demo">
    <targets>
      <name>iar</name>
      <name>slsproj</name>
    </targets>
    <cflags>
      <define>DEBUG_EFM</define>
      <diagsuppress only_ide="iar">Pa050</diagsuppress>
    </cflags>
    <directories>
      <cmsis>$PROJ_DIR$\..\..\..\platform</cmsis>
      <drivers>$PROJ_DIR$\..\..\..\hardware\kit\common\drivers</drivers>
      <bsp>$PROJ_DIR$\..\..\..\hardware\kit\common\bsp</bsp>
      <kitconfig>$PROJ_DIR$\..\..\..\hardware\kit\EFR32MG21_BRD4181A\config</kitconfig>
    </directories>
    <includepaths>
      <path>##em-path-cmsis##\CMSIS\Include</path>
      <path>##em-path-cmsis##\Device\SiliconLabs\EFR32MG21\Include</path>
      <path>##em-path-cmsis##\emlib\inc</path>
      <path>##em-path-drivers##</path>
      <path>##em-path-bsp##</path>
      <path>##em-path-cmsis##\middleware\glib</path>
      <path>##em-path-kitconfig##</path>
      <path>$PROJ_DIR$\..\src</path>
    </includepaths>
    <group name="CMSIS">
      <source>##em-path-cmsis##\Device\SiliconLabs\EFR32MG21\Source\$IDE$\startup_efr32mg21.s</source>
      <source>##em-path-cmsis##\Device\SiliconLabs\EFR32MG21\Source\system_efr32mg21.c</source>
    </group>
    <group name="Drivers" />
    <group name="emlib">
      <source>##em-path-cmsis##\emlib\src\em_acmp.c</source>
      <source>##em-path-cmsis##\emlib\src\em_assert.c</source>
      <source>##em-path-cmsis##\emlib\src\em_emu.c</source>
      <source>##em-path-cmsis##\emlib\src\em_gpio.c</source>
      <source>##em-path-cmsis##\emlib\src\em_system.c</source>
    </group>
    <group name="Source">
      <source>$PROJ_DIR$\..\src\acmp_demo_series2.c</source>
    </group>
  </project>
</workspace>
