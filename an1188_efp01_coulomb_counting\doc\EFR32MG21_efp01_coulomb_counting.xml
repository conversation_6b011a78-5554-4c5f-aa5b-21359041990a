<?xml version='1.0' encoding='utf-8'?>
<workspace name="an1188_EFP01_Coulomb_Counting">
  <project device="EFR32MG21A010F1024IM32"
           name="EFR32MG21_EFP01_Coulomb_Counting">
    <targets>
      <name>slsproj</name>
	  <name>iar</name>
    </targets>
    <directories>
      <cmsis>$PROJ_DIR$\..\..\..\platform</cmsis>
      <device>$PROJ_DIR$\..\..\..\platform\Device\SiliconLabs</device>
      <emlib>$PROJ_DIR$\..\..\..\platform\emlib</emlib>
      <drivers>$PROJ_DIR$\..\..\..\hardware\kit\common\drivers</drivers>
      <bsp>$PROJ_DIR$\..\..\..\hardware\kit\common\bsp</bsp>
      <kitconfig>$PROJ_DIR$\..\..\..\hardware\kit\EFR32MG21_BRD4179B\config</kitconfig>
      <efp>$PROJ_DIR$\..\..\..\hardware\driver\efp</efp>
      <platform>$PROJ_DIR$\..\..\..\platform</platform>
	  <semanager>$PROJ_DIR$\..\..\..\util\third_party\crypto\sl_component\se_manager</semanager>
    </directories>
    <includepaths>
      <path>##em-path-cmsis##\CMSIS\Include</path>
      <path>##em-path-device##\EFR32MG21\Include</path>
      <path>##em-path-emlib##\inc</path>
      <path>##em-path-kitconfig##</path>
      <path>##em-path-bsp##</path>
      <path>##em-path-drivers##</path>
      <path>##em-path-efp##\inc</path>
      <path>##em-path-platform##\common\inc</path>
	  <path>##em-path-platform##\driver\i2cspm\inc</path>
	  <path>##em-path-semanager##\inc</path>
    </includepaths>
    <group name="CMSIS">
      <source>##em-path-device##\EFR32MG21\Source\$IDE$\startup_efr32mg21.s</source>
      <source>##em-path-device##\EFR32MG21\Source\system_efr32mg21.c</source>
    </group>
    <group name="drivers">
      <source>##em-path-drivers##\retargetio.c</source>
      <source>##em-path-drivers##\retargetserial.c</source>
      <source>##em-path-platform##\driver\i2cspm\src\sl_i2cspm.c</source>
	  <source>##em-path-semanager##\src\sl_se_manager.c</source>
    </group>
    <group name="emlib">
      <source>##em-path-cmsis##\emlib\src\em_assert.c</source>
      <source>##em-path-cmsis##\emlib\src\em_cmu.c</source>
      <source>##em-path-cmsis##\emlib\src\em_core.c</source>
      <source>##em-path-cmsis##\emlib\src\em_gpio.c</source>
      <source>##em-path-cmsis##\emlib\src\em_system.c</source>
      <source>##em-path-cmsis##\emlib\src\em_usart.c</source>
      <source>##em-path-cmsis##\emlib\src\em_i2c.c</source>
    </group>
    <group name="efp">
      <source>##em-path-efp##\src\sl_efp.c</source>
    </group>
    <group name="Source">
      <source>$PROJ_DIR$\..\src\main.c</source>
	  <source>$PROJ_DIR$\..\src\sl_efp_inst_config.h</source>
      <source>$PROJ_DIR$\..\src\readme.txt</source>
    </group>
  <libs>
    <lib only_ide="slsproj">m</lib>
  </libs>
  <cflags>
      <define>DEBUG_EFM</define>
      <define>RETARGET_VCOM=1</define>
      <diagsuppress only_ide="iar">Pa050</diagsuppress>
    </cflags>
  </project>
</workspace>
