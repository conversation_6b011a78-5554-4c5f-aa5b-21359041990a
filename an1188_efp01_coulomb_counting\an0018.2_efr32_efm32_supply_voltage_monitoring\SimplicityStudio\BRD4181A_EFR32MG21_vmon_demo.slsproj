<?xml version="1.0" encoding="UTF-8"?>
<project name="BRD4181A_EFR32MG21_vmon_demo" boardCompatibility="brd4181a" partCompatibility=".*efr32mg21a010f1024im32.*" toolchainCompatibility="" contentRoot="../">
  <module id="com.silabs.sdk.exx32.board">
    <exclude pattern=".*" />
  </module>
  <module id="com.silabs.sdk.exx32.common.CMSIS">
    <exclude pattern=".*" />
  </module>
  <module id="com.silabs.sdk.exx32.common.emlib">
    <include pattern="emlib/em_acmp.c" />
    <include pattern="emlib/em_assert.c" />
    <include pattern="emlib/em_emu.c" />
    <include pattern="emlib/em_gpio.c" />
    <include pattern="emlib/em_system.c" />
  </module>
  <module id="com.silabs.sdk.exx32.common.bsp">
    <exclude pattern=".*" />
  </module>
  <module id="com.silabs.sdk.exx32.common.drivers">
    <exclude pattern=".*" />
  </module>
  <module id="com.silabs.sdk.exx32.part">
    <include pattern="CMSIS/.*/startup_.*_.*.s" />
    <include pattern="CMSIS/.*/system_.*.c" />
  </module>
  <module id="com.silabs.sdk.exx32.external.glib">
    <exclude pattern=".*" />
  </module>
  <macroDefinition name="DEBUG_EFM" languageCompatibility="c cpp" />
  <includePath uri="src" />
  <folder name="src">
    <file name="acmp_demo_series2.c" uri="src/acmp_demo_series2.c" />
  </folder>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.flags" value="-c -x assembler-with-cpp -mfloat-abi=softfp -mfpu=fpv4-sp-d16 "/>
</project>