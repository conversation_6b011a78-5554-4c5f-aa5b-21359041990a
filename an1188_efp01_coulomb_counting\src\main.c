/***************************************************************************//**
 * @file main.c
 * @brief Coulomb Counting on VOA for EFR32xG21 + EFP01 board (BRD4179B)
 * @version 1.1
 *******************************************************************************
 * # License
 * <b>Copyright 2020 Silicon Labs, Inc. www.silabs.com</b>
 *******************************************************************************
 *
 * This file is licensed under the Silabs License Agreement. See the file
 * "Silabs_License_Agreement.txt" for details. Before using this software for
 * any purpose, you must agree to the terms of that agreement.
 *
 ******************************************************************************/
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

#include "em_chip.h"
#include "em_cmu.h"

#include "sl_efp.h"
#include "sl_efp_inst_config.h"
#include "retargetserial.h"

// Global Variables
volatile uint32_t msTicks;                // Counts 1ms timeTicks.

sl_efp_handle_data_t efpHandleData;       // EFP instance data structure
sl_efp_handle_t efp = &efpHandleData;     // EFP instance pointer

uint8_t PRESCL;                           // Value of CC_PRESCL bitfield
float cpp;                                // Calculated charge-per-pulse for VOA

/**************************************************************************//**
 * @brief  SysTick Timer set for 1 msec interrupts
 *****************************************************************************/
void initSysTick(void)
{
  SysTick_Config(CMU_ClockFreqGet(cmuClock_CORE) / 1000);
}

/**************************************************************************//**
 * @brief  Delays number of msTicks (1ms each msTick)
 * @param  dlyTicks  Number of ticks to delay
 *****************************************************************************/
void Delay(uint32_t dlyTicks)
{
  uint32_t curTicks;

  curTicks = msTicks;
  while ((msTicks - curTicks) < dlyTicks);
}

/**************************************************************************//**
 * @brief  SysTick_Handler increase msTicks every 1ms
 *****************************************************************************/
void SysTick_Handler(void)
{
  msTicks++;
}

/**************************************************************************//**
 * @brief  Initializes the EFP
 *****************************************************************************/
void initEFP(void)
{
  // Initialize EFP handle and I2C
  sl_efp_init_data_t initEFP = SL_EFP_INSTANCE_INIT;
  sl_efp_init(efp, &initEFP);

  // Reset the EFP to default configurations
  sl_efp_reset(efp);
}

/**************************************************************************//**
 * @brief  Prepares the Coulomb Counter for calibration
 * @param  prescaler  The value to be written to CC_PRESCL bitfield
 *****************************************************************************/
void initCoulombCounter(uint8_t prescaler)
{
  uint8_t voaStatus = 0;

  // Poll VOA Status Live bit to check if VOA voltage is in regulation
  while (!voaStatus) {
    sl_efp_read_register_field(efp, _EFP01_STATUS_LIVE_ADDRESS, &voaStatus,
                               _EFP01_STATUS_LIVE_VOA_INREG_LIVE_MASK,
                               _EFP01_STATUS_LIVE_VOA_INREG_LIVE_SHIFT);
  }

  // Configure the Prescaler
  PRESCL = prescaler;
  sl_efp_write_register_field(efp, _EFP01_CC_CTRL_ADDRESS, PRESCL,
                              _EFP01_CC_CTRL_CC_PRESCL_MASK,
                              _EFP01_CC_CTRL_CC_PRESCL_SHIFT);
}

/**************************************************************************//**
 * @brief  Run the calibration
 * @param  ccl_sel  The value to be written to CCL_SEL bitfield
 * @param  ccl_lvl  The value to be written to CCL_LVL bitfield
 * @param  nreq     The value to be written to CC_CAL_NREQ bitfield
 * @return The number of 10MHz clock cycles counted
 *****************************************************************************/
uint16_t runCalibration(uint8_t ccl_sel, uint8_t ccl_lvl, uint8_t nreq)
{
  uint8_t ccc_isdone;
  uint8_t ccc_msby, ccc_lsby;
  uint16_t clockCount;

  // Configure calibration registers
  sl_efp_write_register_field(efp, EFP01_CC_CAL, ccl_sel,
                              _EFP01_CC_CAL_CCL_SEL_MASK,
                              _EFP01_CC_CAL_CCL_SEL_SHIFT);

  sl_efp_write_register_field(efp, EFP01_CC_CAL, ccl_lvl,
                              _EFP01_CC_CAL_CCL_LVL_MASK,
                              _EFP01_CC_CAL_CCL_LVL_SHIFT);

  sl_efp_write_register_field(efp, EFP01_CC_CAL, nreq,
                              _EFP01_CC_CAL_CC_CAL_NREQ_MASK,
                              _EFP01_CC_CAL_CC_CAL_NREQ_SHIFT);

  // Start Calibration
  sl_efp_write_register_field(efp, _EFP01_CMD_ADDRESS, 1,
                              _EFP01_CMD_CC_CAL_STRT_MASK,
                              _EFP01_CMD_CC_CAL_STRT_SHIFT);

  // Poll CCC_ISDONE bit from STATUS register
  do {
    sl_efp_read_register_field(efp, _EFP01_STATUS_G_ADDRESS, &ccc_isdone,
                                _EFP01_STATUS_G_CCC_ISDONE_MASK,
                                _EFP01_STATUS_G_CCC_ISDONE_SHIFT);
  } while (!ccc_isdone);

  // Read the 10MHz oscillator counts
  sl_efp_read_register(efp, _EFP01_CCC_MSBY_ADDRESS, &ccc_msby);
  sl_efp_read_register(efp, _EFP01_CCC_LSBY_ADDRESS, &ccc_lsby);
  clockCount = (ccc_msby << 8 | ccc_lsby);

  // Clear the CCC_ISDONE bit in the STATUS_G register
  sl_efp_write_register_field(efp, _EFP01_STATUS_G_ADDRESS, 1,
                              _EFP01_STATUS_G_CCC_ISDONE_MASK,
                              _EFP01_STATUS_G_CCC_ISDONE_SHIFT);

  return clockCount;
}

/**************************************************************************//**
 * @brief  Calculates the maximum NREQ value to prevent overflow
 * @param  ccl_lvl  The current load to determine the max NREQ for
 * @return The maximum NREQ value
 *****************************************************************************/
uint8_t calculateMaxNREQ(uint8_t ccl_lvl)
{
  uint8_t nreq, numPulses;
  float clockCount;
  uint8_t maxNREQ;

  // Configure for calibration
  nreq = 0;
  numPulses = (int)powf(2, nreq + 1);
  clockCount = 0;

  // Run calibration
  clockCount = runCalibration(_EFP01_CC_CAL_CCL_SEL_VOA, ccl_lvl, nreq);

  // Turn off calibration load and reset calibration register
  sl_efp_write_register(efp, _EFP01_CC_CAL_ADDRESS, 0x00);

  // Calculate maximum NREQ
  maxNREQ = (uint8_t)(floor(log2(numPulses * 65535 / clockCount) - 1));

  // Ensure output is within programmable range
  if (maxNREQ > 7) {
    maxNREQ = 7;
  }

  return maxNREQ;
}

/**************************************************************************//**
 * @brief  Calibrates the Coulomb Counter and calculates CPP for VOA
 *****************************************************************************/
void calibrateCoulombCounter(void)
{
  uint8_t ccl_lvl, nreq;
  float clockCountLow, clockCountHigh;
  float numPulsesLow, numPulsesHigh;

  // Clear Coulomb counter registers by setting the CC_CLR bit
  sl_efp_write_register_field(efp, _EFP01_CMD_ADDRESS, 1,
                              _EFP01_CMD_CC_CLR_MASK,
                              _EFP01_CMD_CC_CLR_SHIFT);

  // Run calibration at the lower load current (875 uA)
  ccl_lvl = 3;
  nreq = calculateMaxNREQ(ccl_lvl);
  numPulsesLow = powf(2, nreq + 1);
  clockCountLow = runCalibration(_EFP01_CC_CAL_CCL_SEL_VOA, ccl_lvl, nreq);

  // Run calibration at the higher load current (14,000 uA)
  ccl_lvl = 7;
  nreq = calculateMaxNREQ(ccl_lvl);
  numPulsesHigh = powf(2, nreq + 1);
  clockCountHigh = runCalibration(_EFP01_CC_CAL_CCL_SEL_VOA, ccl_lvl, nreq);

  // Turn off calibration load
  sl_efp_write_register_field(efp, EFP01_CC_CAL, _EFP01_CC_CAL_CCL_SEL_NONE,
                              _EFP01_CC_CAL_CCL_SEL_MASK,
                              _EFP01_CC_CAL_CCL_SEL_SHIFT);

  // Calculate CPP using the following equation:
  //
  //         (count_high * count_low) * (I_high - I_low)
  //        ---------------------------------------------
  // CPP =                  10MHzOscfreq
  //        ---------------------------------------------
  //        (np_high * count_low) - (np_low * count_high)
  cpp  = (clockCountHigh * clockCountLow) * (14000.0f - 875.0f) * powf(10, -6);
  cpp /= 10000000.0f;
  cpp /= (numPulsesHigh * clockCountLow) - (numPulsesLow * clockCountHigh);

  // Change units from C to nC
  cpp *= powf(10 ,9);

  printf("\nCPP: %d nC/pulse\n", (int)cpp);
}

/***************************************************************************//**
 * @brief  Enables the Coulomb Counter on the EFP
 ******************************************************************************/
void startCoulombCounter(void)
{
  // Configure the CC_CTRL register to enable all coulomb counters
  sl_efp_write_register_field(efp, _EFP01_CC_CTRL_ADDRESS, 1,
                              _EFP01_CC_CTRL_CC_EN_MASK,
                              _EFP01_CC_CTRL_CC_EN_SHIFT);
}


/***************************************************************************//**
 * @brief  Reads Coulomb Counter register value
 * @return The number of pulses counted by the Coulomb Counter
 ******************************************************************************/
uint16_t readCoulombCounter(void)
{
  uint16_t numPulses;
  uint8_t msby, lsby;

  sl_efp_read_register(efp, _EFP01_CCA_MSBY_ADDRESS, &msby);
  sl_efp_read_register(efp, _EFP01_CCA_LSBY_ADDRESS, &lsby);

  numPulses = (msby << 8) | lsby;

  return numPulses;
}

/***************************************************************************//**
 * @brief  Calculates current and total charge. Displays voltage, total number
           of pulses, total charge, and current at VOA.
 * @param  numPulsesOld  Total number of pulses counted at dt=0
 * @param  numPulsesNew  Total number of pulses counted at dt=time_ms
 * @param  time_ms       Time in milliseconds between each numPulses read
 ******************************************************************************/
void calculateCurrent(uint32_t numPulsesOld, uint32_t numPulsesNew, float time_ms)
{
  float current;
  float totalCharge;
  uint16_t voltage;

  // Calculate current (I = delta_Q/delta_t)
  current = ((float)cpp *((float)numPulsesNew - (float)numPulsesOld) *
            (float)powf(2, (16 - (2 * PRESCL)))) / (1 * powf(10, 6));
  current = current / (time_ms / 1000);

  // Calculate charge (Q = CPP * # pulses)
  totalCharge = ((float)cpp *(float)numPulsesNew *
                (float)powf(2, (16 - (2 * PRESCL)))) / (1 * powf(10, 6));

  // Measure VDDA voltage
  sl_efp_get_vdd_avg(efp, &voltage);

  // Print info to VCOM
  printf("vddb: %d mV    ", voltage);
  printf("total pulses: 0x%04lx    ", numPulsesNew);
  printf("total charge: %d mC    ", (int)totalCharge);
  printf("current: %d uA\n", (int)(current * 1000));
}

/*****************************************************************************/
/*@brief  Main function
 *****************************************************************************/
int main(void)
{
  uint16_t numPulsesOld, numPulsesNew;
  uint32_t numPulsesMSB = 0;

  // Chip Errata
  CHIP_Init();

  // Route printf() to output to VCOM
  RETARGET_SerialInit();
  RETARGET_SerialCrLf(1);

  // Enable SysTick Interrupts
  initSysTick();

  // Enable EFP
  initEFP();

  // Configure Coulomb Counter in preparation for calibration
  initCoulombCounter(3);

  // Calibrate the Coulomb Counter and calculate CPP
  calibrateCoulombCounter();

  // Enable the Coulomb Counter to start counting pulses
  startCoulombCounter();

  // Measure the charge and current every 1000ms
  numPulsesOld = readCoulombCounter();

  while (1) {
    Delay(1000);

    numPulsesNew = readCoulombCounter();

    // Account for numPulses overflow
    if (numPulsesNew < numPulsesOld) {
      numPulsesMSB += 0x10000;
      calculateCurrent(numPulsesMSB - 0x10000 + numPulsesOld,
                       numPulsesMSB + numPulsesNew, 1000);
    } else {
      calculateCurrent(numPulsesMSB + numPulsesOld,
                       numPulsesMSB + numPulsesNew, 1000);
    }

    numPulsesOld = numPulsesNew;
  }
}
