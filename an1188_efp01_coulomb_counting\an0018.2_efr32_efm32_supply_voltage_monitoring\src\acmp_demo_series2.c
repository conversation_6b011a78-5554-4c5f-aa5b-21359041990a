/**************************************************************************//**
 * @file
 * @brief Supply Voltage Monitoring with ACMP Demo
 * @version  1.10

 ******************************************************************************
 * @section License
 * <b>(C) Copyright 2017 Silicon Labs, http://www.silabs.com</b>
 *******************************************************************************
 *
 * This file is licensed under the Silabs License Agreement. See the file
 * "Silabs_License_Agreement.txt" for details. Before using this software for
 * any purpose, you must agree to the terms of that agreement.
 *
 ******************************************************************************/

/*
This project demonstrates the usage of the ACMP module in combination with the VSENSE inputs
to monitor AVDD. VSENSE has a 1/4 voltage divider while VREF uses a configurable divider.
The VREFDIV divider is configured such that AVDD triggers an interrupt when its voltage
crosses ~2.54V threshold voltage. The ACMP interrupt handler toggles the LEDs on the WSTK
and radio board module based on AVDD's voltage.  When AVDD is above this threshold, LED1 on the
WSTK is turned on (along with the green LED on the radio module) and LED0 is turned off. 
When AVDD is below this threshold, LED0 on the WSTK is turned on (along with the red LED on the
radio module) and LED1 is turned off. 

How to Test:
1.  Update the kit's firmware from the Simplicity Launcher (if necessary)
2.  Build the project and download to the Starter Kit
3.  Disconnect Simplicity Studio debugger from the starter kit (just click disconnect 
    button in Simplicity Studio)
4.  Place the Starter Kit power switch into BAT to disconnect the debugger's power
5.  Attach a positive 3.30V power supply to the GND and MCU pins of the WSTK Expansion Header;
    Positive 3.30V should be applied to EXP pin 2 and 0.0V applied to EXP pin 1
6.  Vary the power supply voltage above/below the 2.54V threshold (remaining below 3.30V)
7.  Observe the LED states on the WSTK and radio board module when above and below the threshold

Peripherals Used:
ACMP    - biasProg value of 0x2; default for capsense
          Reduced input range (0 to VDD-0.7V; this is ok because AVDD is connected through a 1/4 
            voltage divider)
          High accuracy mode; Hysterisis disabled; 2.5V internal reference; VSENSE0 connected to AVDD
          VREFDIV set to 0x10 -> 2.5V * (16/63) = 0.6349 * 4 (1/4 voltage divider) = 2.54V threshold 
          Rising/falling/ready interrupts enabled
                         
Board:  Silicon Labs EFR32xG21 Radio Board (BRD4181A) + 
        Wireless Starter Kit Mainboard
Device: EFR32MG21A010F1024IM32
PB00 - GPIO Push/Pull output, Expansion Header Pin 11, WSTK Pin 8, LED0
PB01 - GPIO Push/Pull output, Expansion Header Pin 13, WSTK Pin 10, LED1
VMCU - Expansion Header Pin 2
GND  - Expansion Header Pin 1 
*/
 
#include "em_device.h"
#include "em_chip.h"
#include "em_emu.h"
#include "em_cmu.h"
#include "em_acmp.h"
#include "em_gpio.h"
#include "efr32mg21_acmp.h"

uint32_t AComp0 = 0;

/* Flag to keep the state of the power rails */
volatile bool AVDD_good = true;

/**************************************************************************//**
 * @brief AMP0 Interrupt handler
 *****************************************************************************/
void ACMP0_IRQHandler(void)
{
  /* Clear interrupt flag */
  ACMP0->IF_CLR = ACMP_IF_ACMPRDY;
  ACMP0->IF_CLR = ACMP_IF_FALL;
  ACMP0->IF_CLR = ACMP_IF_RISE;
  
  /* Get output */
  AComp0 = ((ACMP0->STATUS & ACMP_STATUS_ACMPOUT) >> _ACMP_STATUS_ACMPOUT_SHIFT);
  
  if (AComp0 == 0) {
    AVDD_good = false; // VSENSE is below threshold
    GPIO_PinOutSet(gpioPortB, 0);
    GPIO_PinOutClear(gpioPortB, 1);
  } else {
    AVDD_good = true; // VSENSE is above threshold
    GPIO_PinOutSet(gpioPortB, 1);
    GPIO_PinOutClear(gpioPortB, 0);
  }
}
/**************************************************************************//**
 * @brief  Main function
 *
 * In this example we are comparing AVDD (VSENSE0) input to the internal 2.5V
 * reference. When AVDD falls below threshold, LED0 lights up. If AVDD is 
 * above threshold, LED1 lights up.
 * 
 * Note: VREFOUT = VREFIN * (VREFDIV/63). Since VSENSE0 is divided by 4 we need 
 * to also divide VREF to be comparable to VSENSE0. In this example we use
 * VREFDIV = 16 (0x10) to get the reference around 0.7V. The threshold can 
 * be calculated with VREFDIV * (16/63) * 4 = 2.54V.
 *****************************************************************************/
int main(void)
{
  /* Chip errata */
  CHIP_Init();

  /* Initialize ACMP settings */
  ACMP_Init_TypeDef acmp0Init =
  {                                                                           \
    0x2,                      /* Using biasProg value of 0x2. */              \
    acmpInputRangeReduced,    /* Reduced 0 to Vdd-0.7. */                     \
    acmpAccuracyHigh,         /* High accuracy, less current usage. */        \
    acmpHysteresisDisabled,   /* Disable hysteresis. */                       \
    false,                    /* Output 0 when ACMP is inactive. */           \
    0x10,                     /* Set VREFDIV. */                              \
    true                      /* Enable after init. */                        \
  };

  ACMP_Init(ACMP0, &acmp0Init);

  /* Set VREF 2.5V to NEGSEL input and VSENSE0 to POSSEL input */ 
  ACMP_ChannelSet(ACMP0, acmpInputVREFDIV2V5, acmpInputVSENSE01DIV4);

  /* Enable rising/falling/ready interrupts */
  ACMP_IntEnable(ACMP0, ACMP_IEN_RISE);
  ACMP_IntEnable(ACMP0, ACMP_IEN_FALL);
  ACMP_IntEnable(ACMP0, ACMP_IEN_ACMPRDY);
  
  /* Wait for warm up */
  while (!(ACMP0->STATUS & ACMP_STATUS_ACMPRDY));
  
  /* Configure LEDs */
  GPIO_PinModeSet(gpioPortB, 0, gpioModePushPull, 0);
  GPIO_PinModeSet(gpioPortB, 1, gpioModePushPull, 0);
  
  /* To be able to probe the output we can send the ACMP output to a pin.
   * The second argument to this function is the pin location which is
   * device dependent. */
  GPIO_PinModeSet(gpioPortC, 0, gpioModePushPull, 0);
  ACMP_GPIOSetup(ACMP0, gpioPortC, 0, true, false);
  
  /* Enable interrupts */
  NVIC_ClearPendingIRQ(ACMP0_IRQn);
  NVIC_EnableIRQ(ACMP0_IRQn);
  
  while (1)
  {
    /* Enter EM3 until the next interrupt occurs */
    EMU_EnterEM3(true);
  }
}
