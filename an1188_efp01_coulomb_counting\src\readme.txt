This application note is compatible with Gecko SDK Suite 3.0 and 32-bit MCU SDK
********.

This example is intended for use with the BRD4179B board (EFR32xG21 + EFP0104).
On the BRD4179B, the EFP0104's VOA output drives the PAVDD, RFVDD, AVDD, and
DVDD of the EFR32xG21 chip. The EFP0104's VOB output is disabled on startup, but
can drive the DECOUPLE pin if properly enabled.

This example has the following configurations:
VOA                - 1.8V
VOB                - disabled
Prescaler          - 3
Energy Mode        - EM0
Communication Mode - I2C

This example code does the following steps:
1. Configure the EFP0104 to prepare for calibration
2. Calibrate the Coulomb Counter and calculate the CPP value
3. Start the Coulomb Counter and display the input supply voltage, total
   number of pulses, total charge, and current at VOA every 1 second

================================================================================
Step 1 - Configure the EFP0104 to prepare to for calibration
================================================================================
The initEFP function uses the SDK's default settings to configure the
EFR32xG21's GPIO and I2C in order to communicate with the EFP0104. This allows
for the EFR32xG21 to read and write to the EFP01's registers via I2C.

The initCoulombCounter function sets the prescaler value of the Coulomb Counter,
and ensures that VOA is stable before proceeding with the calibration.

================================================================================
Step 2 - Calibrate the Coulomb Counter and calculate the CPP value
================================================================================
The calibrateCoulombCounter function does two calibrations: One calibration at a
lower load current, and another calibration at a higher load current.

For each calibration, the NREQ value is carefully selected to provide the highest
number of PFM pulses without overflowing the CCC_MSBY/CCC_LSBY result registers.
The calculateMaxNREQ function determines this value by running a calibration
sequence with NREQ set to 0. The function then uses the following equation to
calculate the maximum NREQ value:

NREQ_max = floor(LOG2(np * 65535 / count) - 1)

Where: "np" is 2, the number of PFM pulses
       "65535" is 2^16 - 1, the max value of the CCC_MSBY/CCC_LSBY register pair
       "count" is the number of 10MHz osillator clock cycles counted

After the NREQ value is determined, the runCalibration function configures the
CC_CAL register with the CC_SEL, CC_LVL, and CC_CAL_NREQ bitfields. It starts
the calibration process by setting a 1 to the CC_CAL_STRT bitfield, and then 
polls the CCC_ISDONE flag to check when the calibration completes. The
CCC_MSBY/CCC_LSBY result register pairs are read, and the CCC_ISDONE flag is
reset.

Once calibrations at the low and high load currents are complete, the following
equation can be used to calculate the CPP:

       (count_high * count_low) * (I_high - I_low)
      ---------------------------------------------
CPP =                 10MHzOscfreq
      ---------------------------------------------
      (np_high * count_low) - (np_low * count_high)

Where: "count" is the number of 10MHz oscillator clock cycles counted
       "I" is the load current provided by the EFP0104
       "10MHzOscfreq" is 10^7 cycles/second
       "np" is the number of pulses

================================================================================
Step 3. Start the Coulomb Counter and display the input supply voltage, total
        number of pulses, total charge, and current at VOA every 1 second
================================================================================
After the startCoulombCounter function is called, the Coulomb Counter starts
counting the number of PFM pulses from VOA and stores them in the
CCA_MSBY/CCA_LSBY register pair.

The while loop in the main function periodically reads the register pair
accounting for overflow, calculates the current and total charge from VOA, and
displays the input supply voltage, total number of pulses, total charge, and
current via VCOM.

Note that if the input supply voltage is close to the VOA output voltage
(VDDB_MIN - VOA <= 300mV), DCDC A will turn off and LDO C will turn on. The
Coulomb Counter will stop counting to the CCA_MSBY/CCA_LSBY register pair, and 
will start counting to the CCC_MSBY/CCC_LSBY register pair.
